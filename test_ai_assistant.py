#!/usr/bin/env python3
"""
Test script for AI Assistant integration.
Verifies that all components are working correctly.
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from database import DatabaseManager, Message
from message_bridge import MessageBridge
from ai_assistant_service import AIAssistantService


async def test_database():
    """Test database connectivity."""
    print("🔍 Testing database...")
    try:
        db_manager = DatabaseManager("data/test_messages.db")
        await db_manager.initialize()
        
        # Test message creation
        test_message = Message(
            user_id=5295836625,
            username="test_user",
            message_text="Test message for AI assistant",
            message_type="test",
            direction="incoming",
            timestamp=datetime.now()
        )
        
        message_id = await db_manager.save_message(test_message)
        print(f"✅ Database test passed - Message ID: {message_id}")
        
        await db_manager.close()
        
        # Clean up test database
        if os.path.exists("data/test_messages.db"):
            os.remove("data/test_messages.db")
        
        return True
        
    except Exception as e:
        print(f"❌ Database test failed: {e}")
        return False


async def test_config_loading():
    """Test configuration loading."""
    print("🔍 Testing configuration...")
    try:
        with open("config/config.json", "r") as f:
            config = json.load(f)
        
        # Check required sections
        required_sections = ["telegram", "ai_assistant", "database"]
        for section in required_sections:
            if section not in config:
                raise ValueError(f"Missing config section: {section}")
        
        # Check AI assistant config
        ai_config = config["ai_assistant"]
        if not ai_config.get("enabled", False):
            print("⚠️  AI Assistant is disabled in configuration")
            return False
        
        api_key = ai_config.get("api_key") or os.getenv("ANTHROPIC_API_KEY")
        if not api_key or api_key == "YOUR_ANTHROPIC_API_KEY_HERE":
            print("⚠️  Anthropic API key not configured")
            return False
        
        print("✅ Configuration test passed")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def test_ai_assistant_initialization():
    """Test AI assistant service initialization."""
    print("🔍 Testing AI Assistant initialization...")
    try:
        # Load config
        with open("config/config.json", "r") as f:
            config = json.load(f)
        
        # Initialize components
        db_manager = DatabaseManager("data/test_messages.db")
        await db_manager.initialize()
        
        message_bridge = MessageBridge(db_manager, config)
        await message_bridge.initialize()
        
        # Initialize AI assistant
        ai_assistant = AIAssistantService(config, db_manager, message_bridge)
        await ai_assistant.start()
        
        print("✅ AI Assistant initialization test passed")
        
        # Clean up
        await ai_assistant.stop()
        await message_bridge.close()
        await db_manager.close()
        
        if os.path.exists("data/test_messages.db"):
            os.remove("data/test_messages.db")
        
        return True
        
    except Exception as e:
        print(f"❌ AI Assistant initialization test failed: {e}")
        return False


async def test_message_processing():
    """Test message processing workflow."""
    print("🔍 Testing message processing...")
    try:
        # Load config
        with open("config/config.json", "r") as f:
            config = json.load(f)
        
        # Initialize components
        db_manager = DatabaseManager("data/test_messages.db")
        await db_manager.initialize()
        
        message_bridge = MessageBridge(db_manager, config)
        await message_bridge.initialize()
        
        ai_assistant = AIAssistantService(config, db_manager, message_bridge)
        await ai_assistant.start()
        
        # Create test message
        test_message = Message(
            user_id=5295836625,
            username="test_user",
            message_text="This is a test message for the AI assistant",
            message_type="text",
            direction="incoming",
            timestamp=datetime.now()
        )
        
        # Save message
        message_id = await db_manager.save_message(test_message)
        test_message.id = message_id
        
        # Test message detection
        is_task = ai_assistant._is_task_message(test_message)
        print(f"✅ Message task detection: {is_task}")
        
        # Test task creation
        if is_task:
            task = ai_assistant._create_task_from_message(test_message)
            print(f"✅ Task creation: {task.id}")
        
        print("✅ Message processing test passed")
        
        # Clean up
        await ai_assistant.stop()
        await message_bridge.close()
        await db_manager.close()
        
        if os.path.exists("data/test_messages.db"):
            os.remove("data/test_messages.db")
        
        return True
        
    except Exception as e:
        print(f"❌ Message processing test failed: {e}")
        return False


async def test_api_connectivity():
    """Test API connectivity (without making actual calls)."""
    print("🔍 Testing API configuration...")
    try:
        # Load config
        with open("config/config.json", "r") as f:
            config = json.load(f)
        
        ai_config = config.get("ai_assistant", {})
        api_key = ai_config.get("api_key") or os.getenv("ANTHROPIC_API_KEY")
        
        if not api_key or api_key == "YOUR_ANTHROPIC_API_KEY_HERE":
            print("❌ API key not configured")
            return False
        
        # Check if API key format looks correct
        if not api_key.startswith("sk-"):
            print("⚠️  API key format may be incorrect (should start with 'sk-')")
            return False
        
        print("✅ API configuration test passed")
        return True
        
    except Exception as e:
        print(f"❌ API configuration test failed: {e}")
        return False


def print_test_summary(results):
    """Print test summary."""
    print("\n" + "="*50)
    print("🧪 TEST SUMMARY")
    print("="*50)
    
    total_tests = len(results)
    passed_tests = sum(1 for result in results.values() if result)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
    
    print("-"*50)
    print(f"Total: {passed_tests}/{total_tests} tests passed")
    
    if passed_tests == total_tests:
        print("\n🎉 All tests passed! Your AI Assistant is ready to use.")
        print("\nNext steps:")
        print("1. Run: python main.py")
        print("2. Send a message to your Telegram bot")
        print("3. Watch the AI assistant process your request!")
    else:
        print(f"\n⚠️  {total_tests - passed_tests} test(s) failed.")
        print("Please fix the issues above before using the AI Assistant.")
    
    print("="*50)


async def main():
    """Run all tests."""
    print("🚀 Testing AI Assistant Integration...")
    print("="*50)
    
    # Define tests
    tests = [
        ("Database", test_database),
        ("Configuration", test_config_loading),
        ("AI Assistant Init", test_ai_assistant_initialization),
        ("Message Processing", test_message_processing),
        ("API Configuration", test_api_connectivity),
    ]
    
    results = {}
    
    # Run tests
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results[test_name] = False
        print()
    
    # Print summary
    print_test_summary(results)
    
    # Return overall success
    return all(results.values())


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Tests cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
