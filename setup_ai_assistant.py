#!/usr/bin/env python3
"""
Setup script for the AI Assistant integration.
Helps configure the remote AI assistant workflow.
"""

import os
import json
import sys
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 10):
        print("❌ Error: Python 3.10 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version}")
    return True


def check_config_file():
    """Check if configuration file exists."""
    config_path = Path("config/config.json")
    if not config_path.exists():
        print("❌ Configuration file not found")
        print("Please copy config/config.example.json to config/config.json")
        return False
    print("✅ Configuration file found")
    return True


def check_api_key():
    """Check if Anthropic API key is configured."""
    try:
        with open("config/config.json", "r") as f:
            config = json.load(f)
        
        ai_config = config.get("ai_assistant", {})
        api_key = ai_config.get("api_key") or os.getenv("ANTHROPIC_API_KEY")
        
        if not api_key or api_key == "YOUR_ANTHROPIC_API_KEY_HERE":
            print("❌ Anthropic API key not configured")
            print("Please set your API key in config/config.json or as ANTHROPIC_API_KEY environment variable")
            print("Get your API key from: https://console.anthropic.com/")
            return False
        
        print("✅ Anthropic API key configured")
        return True
        
    except Exception as e:
        print(f"❌ Error checking API key: {e}")
        return False


def check_telegram_config():
    """Check if Telegram bot is configured."""
    try:
        with open("config/config.json", "r") as f:
            config = json.load(f)
        
        telegram_config = config.get("telegram", {})
        bot_token = telegram_config.get("bot_token")
        allowed_users = telegram_config.get("allowed_users", [])
        
        if not bot_token or bot_token == "YOUR_BOT_TOKEN_HERE":
            print("❌ Telegram bot token not configured")
            print("Please set your bot token in config/config.json")
            print("Get a bot token from @BotFather on Telegram")
            return False
        
        if not allowed_users or 123456789 in allowed_users:
            print("⚠️  Warning: Default user ID found in allowed_users")
            print("Please update allowed_users with your actual Telegram user ID")
            print("You can get your user ID by messaging @userinfobot on Telegram")
        
        print("✅ Telegram configuration found")
        return True
        
    except Exception as e:
        print(f"❌ Error checking Telegram config: {e}")
        return False


def enable_ai_assistant():
    """Enable the AI assistant in configuration."""
    try:
        with open("config/config.json", "r") as f:
            config = json.load(f)
        
        # Ensure ai_assistant section exists and is enabled
        if "ai_assistant" not in config:
            config["ai_assistant"] = {
                "api_key": "YOUR_ANTHROPIC_API_KEY_HERE",
                "model": "claude-3-sonnet-20240229",
                "max_tokens": 4000,
                "update_interval": 30,
                "enabled": True,
                "auto_process_messages": True,
                "context_memory_limit": 5
            }
        else:
            config["ai_assistant"]["enabled"] = True
            config["ai_assistant"]["auto_process_messages"] = True
        
        # Write back to file
        with open("config/config.json", "w") as f:
            json.dump(config, f, indent=2)
        
        print("✅ AI Assistant enabled in configuration")
        return True
        
    except Exception as e:
        print(f"❌ Error enabling AI assistant: {e}")
        return False


def create_env_file():
    """Create a .env file template."""
    env_content = """# Anthropic API Key for AI Assistant
# Get your key from: https://console.anthropic.com/
ANTHROPIC_API_KEY=your_api_key_here

# Optional: Override model settings
# AI_MODEL=claude-3-sonnet-20240229
# AI_MAX_TOKENS=4000
"""
    
    try:
        with open(".env", "w") as f:
            f.write(env_content)
        print("✅ Created .env file template")
        return True
    except Exception as e:
        print(f"❌ Error creating .env file: {e}")
        return False


def print_usage_instructions():
    """Print usage instructions for the AI assistant."""
    print("\n" + "="*60)
    print("🤖 AI ASSISTANT SETUP COMPLETE!")
    print("="*60)
    print()
    print("📋 USAGE INSTRUCTIONS:")
    print()
    print("1. Start the system:")
    print("   python main.py")
    print()
    print("2. Send messages to your Telegram bot:")
    print("   • Any message will be processed as a task")
    print("   • Use /ask <question> for specific questions")
    print("   • The AI will send status updates while working")
    print("   • Results will be sent back when complete")
    print()
    print("3. Example messages to try:")
    print("   • 'Help me write a Python function to sort a list'")
    print("   • 'Explain how async/await works in Python'")
    print("   • 'Create a simple web scraper example'")
    print()
    print("4. Features:")
    print("   • ✅ Real-time status updates")
    print("   • ✅ Context memory across conversations")
    print("   • ✅ Automatic task detection")
    print("   • ✅ Error handling and retry logic")
    print()
    print("🔧 CONFIGURATION:")
    print("   • Edit config/config.json to customize settings")
    print("   • Set ANTHROPIC_API_KEY environment variable")
    print("   • Check logs in data/app.log for troubleshooting")
    print()
    print("="*60)


def main():
    """Main setup function."""
    print("🚀 Setting up AI Assistant for Telegram MCP...")
    print()
    
    # Check prerequisites
    checks = [
        ("Python Version", check_python_version),
        ("Configuration File", check_config_file),
        ("Telegram Configuration", check_telegram_config),
        ("Anthropic API Key", check_api_key),
    ]
    
    all_passed = True
    for name, check_func in checks:
        print(f"Checking {name}...")
        if not check_func():
            all_passed = False
        print()
    
    if not all_passed:
        print("❌ Some checks failed. Please fix the issues above and run setup again.")
        return False
    
    # Enable AI assistant
    print("Enabling AI Assistant...")
    if not enable_ai_assistant():
        return False
    print()
    
    # Create .env file if it doesn't exist
    if not os.path.exists(".env"):
        print("Creating .env file template...")
        create_env_file()
        print()
    
    # Print usage instructions
    print_usage_instructions()
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Setup cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        sys.exit(1)
