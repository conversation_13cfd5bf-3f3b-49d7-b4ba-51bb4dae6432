#!/usr/bin/env python3
"""
Test script for the new start_claude_session functionality.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add current directory to path for imports
sys.path.insert(0, str(Path(__file__).parent))

from database import DatabaseManager, Message
from message_bridge import MessageBridge
from mcp_server import start_claude_session_impl


async def test_start_session():
    """Test the start_claude_session functionality."""
    print("🧪 Testing start_claude_session...")
    
    try:
        # Load config
        with open("config/config.json", "r") as f:
            config = json.load(f)
        
        # Initialize components (minimal setup)
        db_manager = DatabaseManager("data/test_session.db")
        await db_manager.initialize()
        
        message_bridge = MessageBridge(db_manager, config)
        await message_bridge.initialize()
        
        # Set global variables (simulating MCP server environment)
        import mcp_server
        mcp_server.db_manager = db_manager
        mcp_server.message_bridge = message_bridge
        
        # Test the start_claude_session function
        result = await start_claude_session_impl({"user_id": 5295836625})
        
        print("✅ start_claude_session result:")
        print(result.content[0].text)
        
        # Check if message was saved to database
        messages = await db_manager.get_messages(user_id=5295836625, limit=1)
        if messages:
            print(f"✅ Message saved to database: {messages[0].message_text[:50]}...")
        else:
            print("❌ No message found in database")
        
        # Cleanup
        await message_bridge.close()
        await db_manager.close()
        
        # Remove test database
        import os
        if os.path.exists("data/test_session.db"):
            os.remove("data/test_session.db")
        
        print("✅ Test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


async def main():
    """Run the test."""
    print("🚀 Testing Start Claude Session Functionality")
    print("=" * 50)
    
    success = await test_start_session()
    
    if success:
        print("\n🎉 All tests passed!")
        print("\nYour 'start new session' functionality is ready!")
        print("\nTo use it:")
        print("1. Start your main system: python main.py")
        print("2. In a Claude session, say: 'start new session'")
        print("3. I'll call start_claude_session and send you a message on Telegram")
        print("4. You can then control me through Telegram messages!")
    else:
        print("\n❌ Tests failed. Please check the errors above.")
    
    return success


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Test cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        sys.exit(1)
