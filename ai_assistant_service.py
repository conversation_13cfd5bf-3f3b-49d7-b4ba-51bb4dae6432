"""
AI Assistant Service for Remote Task Processing via Telegram MCP.
Provides autonomous AI assistant capabilities through Telegram integration.
"""

import asyncio
import json
import logging
import os
from datetime import datetime
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

import httpx
from database import DatabaseManager, Message
from message_bridge import MessageBridge

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task processing status."""
    PENDING = "pending"
    WORKING = "working"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class Task:
    """Represents a task to be processed by the AI assistant."""
    id: Optional[str] = None
    user_id: int = 0
    username: Optional[str] = None
    task_prompt: str = ""
    task_type: str = "general"  # general, code, analysis, etc.
    status: TaskStatus = TaskStatus.PENDING
    created_at: Optional[datetime] = None
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    result: Optional[str] = None
    error_message: Optional[str] = None
    context: Optional[Dict[str, Any]] = None


class AIAssistantService:
    """
    AI Assistant Service that processes tasks from Telegram messages
    and provides autonomous AI assistance capabilities.
    """
    
    def __init__(self, config: Dict[str, Any], db_manager: DatabaseManager, 
                 message_bridge: MessageBridge):
        self.config = config
        self.db_manager = db_manager
        self.message_bridge = message_bridge
        
        # AI API configuration
        self.ai_config = config.get("ai_assistant", {})
        self.api_key = self.ai_config.get("api_key") or os.getenv("ANTHROPIC_API_KEY")
        self.model = self.ai_config.get("model", "claude-3-sonnet-20240229")
        self.max_tokens = self.ai_config.get("max_tokens", 4000)
        
        # Task processing
        self.active_tasks: Dict[str, Task] = {}
        self.task_queue: asyncio.Queue[Task] = asyncio.Queue()
        self.is_running = False
        self.processing_task: Optional[asyncio.Task] = None
        
        # User context storage
        self.user_contexts: Dict[int, Dict[str, Any]] = {}
        
        # HTTP client for AI API calls
        self.http_client: Optional[httpx.AsyncClient] = None
        
        # Allowed users (from config)
        self.allowed_users = set(config.get("telegram", {}).get("allowed_users", []))
        
        logger.info("AI Assistant Service initialized")
    
    async def start(self):
        """Start the AI assistant service."""
        if self.is_running:
            logger.warning("AI Assistant Service is already running")
            return
        
        # Initialize HTTP client
        self.http_client = httpx.AsyncClient(timeout=60.0)
        
        # Start task processing
        self.processing_task = asyncio.create_task(self._process_tasks())
        self.is_running = True
        
        logger.info("🤖 AI Assistant Service started")
    
    async def stop(self):
        """Stop the AI assistant service."""
        if not self.is_running:
            return
        
        self.is_running = False
        
        # Cancel processing task
        if self.processing_task:
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
        
        # Close HTTP client
        if self.http_client:
            await self.http_client.aclose()
        
        logger.info("🛑 AI Assistant Service stopped")
    
    async def process_message(self, message: Message) -> bool:
        """
        Process an incoming message and determine if it's a task request.
        Returns True if the message was processed as a task.
        """
        try:
            # Check if user is allowed
            if message.user_id not in self.allowed_users:
                logger.warning(f"Unauthorized user {message.user_id} attempted to send task")
                return False
            
            # Check if this is a task-related message
            if not self._is_task_message(message):
                return False
            
            # Create task from message
            task = self._create_task_from_message(message)
            
            # Add to queue
            await self.task_queue.put(task)
            
            # Send acknowledgment
            await self._send_status_update(task, "📨 Task received! Starting work...")
            
            logger.info(f"Queued task {task.id} from user {message.user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error processing message as task: {e}")
            return False
    
    def _is_task_message(self, message: Message) -> bool:
        """Determine if a message should be treated as a task request."""
        # Check message type
        if message.message_type in ["command", "question", "task"]:
            return True
        
        # Check for task keywords in regular messages
        task_keywords = ["help me", "can you", "please", "task:", "do this:", "execute:"]
        text_lower = message.message_text.lower()
        
        return any(keyword in text_lower for keyword in task_keywords)
    
    def _create_task_from_message(self, message: Message) -> Task:
        """Create a Task object from a Message."""
        task_id = f"task_{message.user_id}_{int(datetime.now().timestamp())}"
        
        # Determine task type
        task_type = "general"
        if "code" in message.message_text.lower():
            task_type = "code"
        elif "analyze" in message.message_text.lower():
            task_type = "analysis"
        
        return Task(
            id=task_id,
            user_id=message.user_id,
            username=message.username,
            task_prompt=message.message_text,
            task_type=task_type,
            status=TaskStatus.PENDING,
            created_at=datetime.now(),
            context={"original_message_id": message.id}
        )
    
    async def _process_tasks(self):
        """Main task processing loop."""
        logger.info("Task processing loop started")
        
        while self.is_running:
            try:
                # Get next task (with timeout to allow checking is_running)
                try:
                    task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue
                
                # Process the task
                await self._execute_task(task)
                
            except asyncio.CancelledError:
                logger.info("Task processing cancelled")
                break
            except Exception as e:
                logger.error(f"Error in task processing loop: {e}")
                await asyncio.sleep(1)
        
        logger.info("Task processing loop ended")
    
    async def _execute_task(self, task: Task):
        """Execute a single task."""
        try:
            # Update task status
            task.status = TaskStatus.WORKING
            task.started_at = datetime.now()
            self.active_tasks[task.id] = task
            
            # Send working status
            await self._send_status_update(task, "🔄 Working on your task...")
            
            # Get user context
            user_context = self.user_contexts.get(task.user_id, {})
            
            # Prepare AI prompt
            ai_prompt = self._prepare_ai_prompt(task, user_context)
            
            # Send periodic working updates
            update_task = asyncio.create_task(self._send_periodic_updates(task))
            
            try:
                # Call AI API
                result = await self._call_ai_api(ai_prompt)
                
                # Update task with result
                task.result = result
                task.status = TaskStatus.COMPLETED
                task.completed_at = datetime.now()
                
                # Send completion message
                await self._send_completion_message(task)
                
                # Update user context
                self._update_user_context(task, result)
                
                logger.info(f"Task {task.id} completed successfully")
                
            finally:
                # Cancel periodic updates
                update_task.cancel()
                try:
                    await update_task
                except asyncio.CancelledError:
                    pass
            
        except Exception as e:
            # Handle task failure
            task.status = TaskStatus.FAILED
            task.error_message = str(e)
            task.completed_at = datetime.now()
            
            await self._send_error_message(task, str(e))
            logger.error(f"Task {task.id} failed: {e}")
        
        finally:
            # Remove from active tasks
            self.active_tasks.pop(task.id, None)
    
    def _prepare_ai_prompt(self, task: Task, user_context: Dict[str, Any]) -> str:
        """Prepare the prompt for the AI API call."""
        system_prompt = """You are a helpful AI assistant accessed remotely via Telegram. 
        The user has sent you a task to complete. Please provide a comprehensive and helpful response.
        
        Guidelines:
        - Be thorough and detailed in your responses
        - If the task involves code, provide complete, working examples
        - If the task is unclear, ask clarifying questions
        - Maintain context from previous interactions when relevant
        - Be professional but friendly in your communication style
        """
        
        # Add user context if available
        context_info = ""
        if user_context:
            context_info = f"\n\nPrevious context: {json.dumps(user_context, indent=2)}"
        
        full_prompt = f"{system_prompt}\n\nUser task: {task.task_prompt}{context_info}"
        return full_prompt

    async def _call_ai_api(self, prompt: str) -> str:
        """Make an API call to the AI service (Claude API)."""
        if not self.api_key:
            raise ValueError("AI API key not configured")

        headers = {
            "Content-Type": "application/json",
            "x-api-key": self.api_key,
            "anthropic-version": "2023-06-01"
        }

        payload = {
            "model": self.model,
            "max_tokens": self.max_tokens,
            "messages": [
                {
                    "role": "user",
                    "content": prompt
                }
            ]
        }

        try:
            response = await self.http_client.post(
                "https://api.anthropic.com/v1/messages",
                headers=headers,
                json=payload
            )
            response.raise_for_status()

            result = response.json()
            return result["content"][0]["text"]

        except httpx.HTTPError as e:
            logger.error(f"HTTP error calling AI API: {e}")
            raise
        except Exception as e:
            logger.error(f"Error calling AI API: {e}")
            raise

    async def _send_status_update(self, task: Task, status_message: str):
        """Send a status update message to the user."""
        try:
            message = Message(
                user_id=task.user_id,
                username=task.username,
                message_text=status_message,
                message_type="status_update",
                direction="outgoing",
                timestamp=datetime.now(),
                metadata={"task_id": task.id}
            )

            await self.message_bridge.send_to_telegram(message)

        except Exception as e:
            logger.error(f"Error sending status update: {e}")

    async def _send_periodic_updates(self, task: Task):
        """Send periodic 'Working...' updates while task is processing."""
        try:
            update_interval = self.ai_config.get("update_interval", 30)  # seconds
            update_count = 0

            while task.status == TaskStatus.WORKING:
                await asyncio.sleep(update_interval)

                if task.status != TaskStatus.WORKING:
                    break

                update_count += 1
                if update_count % 2 == 0:
                    await self._send_status_update(task, "🔄 Still working on your task...")
                else:
                    await self._send_status_update(task, "⚙️ Processing...")

        except asyncio.CancelledError:
            pass
        except Exception as e:
            logger.error(f"Error sending periodic updates: {e}")

    async def _send_completion_message(self, task: Task):
        """Send task completion message with results."""
        try:
            # Prepare completion message
            completion_text = f"✅ **Task Completed**\n\n{task.result}"

            # Truncate if too long for Telegram
            max_length = self.config.get("telegram", {}).get("max_message_length", 4096)
            if len(completion_text) > max_length:
                truncated_result = task.result[:max_length - 200]
                completion_text = f"✅ **Task Completed**\n\n{truncated_result}\n\n... (result truncated)"

            message = Message(
                user_id=task.user_id,
                username=task.username,
                message_text=completion_text,
                message_type="task_result",
                direction="outgoing",
                timestamp=datetime.now(),
                metadata={"task_id": task.id, "task_type": task.task_type}
            )

            await self.message_bridge.send_to_telegram(message)

        except Exception as e:
            logger.error(f"Error sending completion message: {e}")

    async def _send_error_message(self, task: Task, error: str):
        """Send task error message to user."""
        try:
            error_text = f"❌ **Task Failed**\n\nError: {error}\n\nPlease try again or rephrase your request."

            message = Message(
                user_id=task.user_id,
                username=task.username,
                message_text=error_text,
                message_type="task_error",
                direction="outgoing",
                timestamp=datetime.now(),
                metadata={"task_id": task.id, "error": error}
            )

            await self.message_bridge.send_to_telegram(message)

        except Exception as e:
            logger.error(f"Error sending error message: {e}")

    def _update_user_context(self, task: Task, result: str):
        """Update user context with task results for future reference."""
        user_id = task.user_id

        if user_id not in self.user_contexts:
            self.user_contexts[user_id] = {
                "recent_tasks": [],
                "preferences": {},
                "last_interaction": None
            }

        context = self.user_contexts[user_id]

        # Add to recent tasks (keep last 5)
        task_summary = {
            "task_id": task.id,
            "prompt": task.task_prompt[:100] + "..." if len(task.task_prompt) > 100 else task.task_prompt,
            "type": task.task_type,
            "completed_at": task.completed_at.isoformat() if task.completed_at else None,
            "result_preview": result[:200] + "..." if len(result) > 200 else result
        }

        context["recent_tasks"].append(task_summary)
        if len(context["recent_tasks"]) > 5:
            context["recent_tasks"] = context["recent_tasks"][-5:]

        context["last_interaction"] = datetime.now().isoformat()

    async def get_task_status(self, task_id: str) -> Optional[Task]:
        """Get the status of a specific task."""
        return self.active_tasks.get(task_id)

    async def cancel_task(self, task_id: str, user_id: int) -> bool:
        """Cancel a running task (if user owns it)."""
        task = self.active_tasks.get(task_id)
        if not task or task.user_id != user_id:
            return False

        task.status = TaskStatus.CANCELLED
        await self._send_status_update(task, "🚫 Task cancelled by user")
        return True

    def get_user_context(self, user_id: int) -> Dict[str, Any]:
        """Get user context for debugging or analysis."""
        return self.user_contexts.get(user_id, {})
