# 🤖 Remote AI Assistant via Telegram MCP

This system transforms your existing Telegram MCP notification system into a powerful remote AI assistant that you can access from anywhere via Telegram.

## 🌟 Features

### **Remote Task Processing**
- Send any message to your Telegram bot and it will be processed as an AI task
- Real-time status updates while the AI is working
- Comprehensive results sent back through Telegram
- Context memory across conversations

### **Intelligent Message Detection**
- Automatically detects task-related messages
- Supports both commands (`/ask`) and natural language
- Handles various task types (coding, analysis, general questions)

### **Status Updates**
- "Working..." messages sent periodically during processing
- Error handling with user-friendly error messages
- Task completion summaries with full results

### **Context Awareness**
- Remembers recent conversations and tasks
- Maintains user preferences and interaction history
- Provides contextual responses based on previous interactions

## 🚀 Quick Start

### 1. Setup
```bash
# Run the setup script
python setup_ai_assistant.py

# Install additional dependencies
pip install anthropic>=0.7.0
```

### 2. Configure API Key
Add your Anthropic API key to `config/config.json`:
```json
{
  "ai_assistant": {
    "api_key": "your_anthropic_api_key_here",
    "enabled": true
  }
}
```

Or set as environment variable:
```bash
export ANTHROPIC_API_KEY="your_api_key_here"
```

### 3. Start the System
```bash
python main.py
```

### 4. Use via Telegram
Send any message to your bot:
- `"Help me write a Python function to sort a list"`
- `"Explain how async/await works"`
- `"/ask What's the best way to handle errors in Python?"`

## 📋 Workflow Example

```
You: "Help me create a simple web scraper"
Bot: 📨 Task received! Starting work...

Bot: 🔄 Working on your task...

Bot: ⚙️ Processing...

Bot: ✅ Task Completed

Here's a simple web scraper example using Python:

```python
import requests
from bs4 import BeautifulSoup

def scrape_website(url):
    try:
        response = requests.get(url)
        response.raise_for_status()
        
        soup = BeautifulSoup(response.content, 'html.parser')
        
        # Extract title
        title = soup.find('title').text if soup.find('title') else 'No title'
        
        # Extract all links
        links = [a.get('href') for a in soup.find_all('a', href=True)]
        
        return {
            'title': title,
            'links': links[:10]  # First 10 links
        }
    except Exception as e:
        return {'error': str(e)}

# Usage
result = scrape_website('https://example.com')
print(result)
```

This scraper uses requests to fetch the page and BeautifulSoup to parse HTML...
```

## ⚙️ Configuration

### AI Assistant Settings (`config/config.json`)
```json
{
  "ai_assistant": {
    "api_key": "your_anthropic_api_key_here",
    "model": "claude-3-sonnet-20240229",
    "max_tokens": 4000,
    "update_interval": 30,
    "enabled": true,
    "auto_process_messages": true,
    "context_memory_limit": 5
  }
}
```

### Settings Explained
- **`api_key`**: Your Anthropic API key
- **`model`**: Claude model to use (sonnet, haiku, opus)
- **`max_tokens`**: Maximum response length
- **`update_interval`**: Seconds between "Working..." updates
- **`enabled`**: Enable/disable AI assistant
- **`auto_process_messages`**: Automatically process all messages as tasks
- **`context_memory_limit`**: Number of recent tasks to remember

## 🔧 Advanced Usage

### Task Types
The system automatically detects different task types:
- **General**: Regular questions and requests
- **Code**: Programming-related tasks
- **Analysis**: Data analysis and research tasks

### Message Patterns
These message patterns are automatically detected as tasks:
- Questions: "Can you help me...", "How do I..."
- Commands: "Create a...", "Write a...", "Explain..."
- Explicit tasks: "Task: ...", "Do this: ..."

### Context Memory
The AI remembers:
- Your last 5 tasks and their results
- Your preferences and interaction style
- Previous conversation context

## 🛠️ Technical Architecture

### Components
1. **AI Assistant Service** (`ai_assistant_service.py`)
   - Processes incoming messages
   - Manages task queue and execution
   - Handles AI API communication

2. **Enhanced Telegram Bot** (`telegram_bot.py`)
   - Integrates with AI assistant service
   - Maintains backward compatibility with MCP

3. **Message Bridge** (`message_bridge.py`)
   - Routes messages between components
   - Handles fallback to original MCP functionality

### Message Flow
```
Telegram Message → Bot → AI Assistant → Claude API → Response → Telegram
```

## 🔍 Troubleshooting

### Common Issues

**"AI agent connection not available"**
- Check if AI assistant is enabled in config
- Verify API key is set correctly
- Check logs in `data/app.log`

**No response from AI**
- Check internet connection
- Verify Anthropic API key is valid
- Check API rate limits

**Rate limiting errors**
- Reduce message frequency
- Check Anthropic API usage limits
- Increase `update_interval` in config

### Debug Mode
Enable debug logging in `config/config.json`:
```json
{
  "logging": {
    "level": "DEBUG"
  }
}
```

## 📊 Monitoring

### Logs
- Application logs: `data/app.log`
- Task processing logs include timing and status
- Error logs with full stack traces

### Database
- All messages stored in `data/messages.db`
- Task history and user context preserved
- Query database for analytics

## 🔒 Security

### Access Control
- Only authorized users can send tasks
- User ID validation on all requests
- Rate limiting prevents abuse

### API Security
- API keys stored securely in config
- HTTPS communication with Anthropic
- No sensitive data logged

## 🚀 Future Enhancements

Planned features:
- [ ] Multi-model support (GPT-4, Gemini)
- [ ] File upload processing
- [ ] Voice message transcription
- [ ] Scheduled tasks
- [ ] Team collaboration features
- [ ] Custom prompt templates
- [ ] Integration with external tools

## 📞 Support

For issues or questions:
1. Check the logs in `data/app.log`
2. Review configuration in `config/config.json`
3. Run `python setup_ai_assistant.py` to verify setup
4. Check Telegram bot permissions and API keys

## 📄 License

This AI Assistant extension maintains the same license as the original Telegram MCP project.
